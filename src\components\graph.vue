<script setup>
import * as echarts from 'echarts'
import { ref, onMounted, nextTick, defineProps, defineExpose, computed, onUnmounted } from 'vue'

const props = defineProps({
  series: {
    type: Array,
    default: () => []
  },
  showLegend: {
    type: Boolean,
    default: true
  }
})



const emit = defineEmits(['nodeClick'])

const chartRef = ref(null)
const minHeight = 0 // 设置为0表示无最小高度限制
// 可配置每行显示数量
const itemsPerRow = computed(() => {
  return props.series.length > 4 ? 4 : props.series.length
})
const getGraphSome = (nodesCount) => {
  // 点大小范围
  const MIN_SYMBOL    = 40;
  const MAX_SYMBOL    = 50;
  // 互斥力范围
  const MIN_REPULSION = 30;
  const MAX_REPULSION = 100;

  // 线条长度范围
  const MIN_EDGE_LEN  = 10;
  const MAX_EDGE_LEN  = 80;

  // 假设节点数在 1～100 之间；超过则按边界处理
  const n = Math.max(1, Math.min(nodesCount, 100));

  // 归一化系数
  const t = (n - 1) / (100 - 1);

  // 从 max → min 线性插值
  const lerp = (min, max, t) => max - (max - min) * t;

  return {
    symbolSize: lerp(MIN_SYMBOL,    MAX_SYMBOL,    t),
    force: {
      // initLayout: 'circular',
      // repulsion:  lerp(MIN_REPULSION, MAX_REPULSION, t),
      // edgeLength: lerp(MIN_EDGE_LEN,  MAX_EDGE_LEN,  t),
      repulsion: 500,
      edgeLength: lerp(MIN_EDGE_LEN, MAX_EDGE_LEN, t),
      // gravity: 0.3,
    },
  };
};
const seriesData = computed(() => {
  return props.series.map(item => {
    // 拷贝nodes，避免直接修改props
    const nodes = item.nodes ? item.nodes.map((node, idx) => {
      if (idx < 5 && node.normal_ratio < 0.5) {
        return {
          ...node,
          itemStyle: { ...(node.itemStyle || {}), color: 'purple' }
        }
      }
      if (idx < 5) {
        return {
          ...node,
          itemStyle: { ...(node.itemStyle || {}), color: '#E6A23C' }
        }
      }
      if (node.normal_ratio < 0.5) {
        return {
          ...node,
          itemStyle: { ...(node.itemStyle || {}), color: 'red' }
        }
      }
      return node
    }) : []
    return {
      ...item,
      nodes,
      ...getGraphSome(nodes.length), // 设置节点大小
    }
  })
})


const calculateLayout = () => {
  const margin = 5 // 边距百分比
  const chartWidth = (100 - margin * (itemsPerRow.value + 1)) / itemsPerRow.value
  const rowCount = Math.ceil(seriesData.value.length / itemsPerRow.value)
  const containerHeight = 100 // 容器高度百分比

  // 计算基础高度（无最小限制时）
  let chartHeight = (containerHeight - margin * (rowCount + 1)) / rowCount

  // 如果有最小高度限制且计算结果小于最小值
  if (minHeight > 0 && chartHeight < minHeight) {
    chartHeight = minHeight
  }

  return seriesData.value.map((_, index) => {
    const row = Math.floor(index / itemsPerRow.value)
    const col = index % itemsPerRow.value
    return {
      left: `${margin + col * (chartWidth + margin)}%`,
      top: `${margin + row * (chartHeight + margin)}%`,
      width: `${chartWidth}%`,
      height: `${chartHeight}%`
    }
  })
}

let chart = null; // 新增全局变量
let resizeHandler = null; // 新增resize处理函数变量

onMounted(() => {
  nextTick(() => {
    chart = echarts.init(chartRef.value)
    const layouts = calculateLayout()

    const option = {
      color: ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f'],
      series: seriesData.value.map((data, i) => ({
        name: `关系${i+1}`,
        type: 'graph',
        layout: 'force',
        roam: true,
        draggable: true, // 允许节点拖拽
        label: { show: true, color: '#fff' },
        ...layouts[i],
        ...data,
      }))
    }

    chart.setOption(option)
    
    // 添加节点点击事件监听
    chart.on('click', 'series.graph', (params) => {
      if (params.dataType === 'node') {
        emit('nodeClick', params.data)
      }
    })
    
    resizeHandler = () => chart && chart.resize();
    window.addEventListener('resize', resizeHandler);
    // 在2秒后，fixed所有节点
    /* setTimeout(() => {
      const opt = {...chart.getOption()};
      opt.series.forEach(item => {
        item.nodes.forEach((node, index) => {
          node.fixed = true;
        });
      })
      chart.setOption(opt);
    }, 5000) */
  })
})
const exportImg = () => {
  if (!chart) return;

  const chartDataURL = chart.getDataURL({
    type: 'png',
    backgroundColor: '#fff'
  })

  const link = document.createElement('a')
  link.href = chartDataURL
  link.download = `研判结果_${+new Date()}.png`
  link.click()
}
const updateData = (data) => {
  // 销毁旧实例前移除resize监听
  if (chart) {
    if (resizeHandler) {
      window.removeEventListener('resize', resizeHandler);
      resizeHandler = null;
    }
    chart.dispose();
  }
  chart = echarts.init(chartRef.value);
  const layouts = calculateLayout();
  // 处理每个series的前五个节点为红色
  const processedData = data.map(item => {
    const nodes = item.nodes ? item.nodes.map((node, idx) => {
      if (idx < 5 && node.normal_ratio < 0.5) {
        return {
          ...node,
          itemStyle: { ...(node.itemStyle || {}), color: 'purple' }
        }
      }
      if (node.normal_ratio < 0.5) {
        return {
          ...node,
          itemStyle: { ...(node.itemStyle || {}), color: 'red' }
        }
      }
      if (idx < 5) {
        return {
          ...node,
          itemStyle: { ...(node.itemStyle || {}), color: '#E6A23C' }
        }
      }
      return node
    }) : []
    return {
      ...item,
      nodes,
    }
  })
  const option = {
    color: ['#26b7ff', '#21bcdb', '#2e99e5', '#2e5ce5', '#5c67e5', '#39bfb4', '#6159b2', '#7a5c99', '#995c8f'],
    series: processedData.map((item, i) => ({
      name: `关系${i+1}`,
      type: 'graph',
      layout: 'force',
      roam: true,
      draggable: true, // 允许节点拖拽
      label: { show: true, color: '#fff' },
      ...layouts[i],
      ...getGraphSome(item.nodes.length),
      ...item,
    }))
  };
  chart.setOption(option);
  
  // 重新绑定点击事件
  chart.on('click', 'series.graph', (params) => {
    if (params.dataType === 'node') {
      emit('nodeClick', params.data)
    }
  })
  
  // 重新绑定resize监听
  resizeHandler = () => chart && chart.resize();
  window.addEventListener('resize', resizeHandler);
}
// 组件卸载时彻底清理
onUnmounted(() => {
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler);
    resizeHandler = null;
  }
  if (chart) {
    chart.dispose();
    chart = null;
  }
});
defineExpose({
  exportImg,
  updateData
})
</script>


<template>
  <div style="position: relative; width:100%; height:100%; min-height:600px; overflow:auto">
    <div ref="chartRef" style="width:100%; height:100%;"></div>

    <!-- 图例 -->
    <div v-if="showLegend" class="graph-legend">
      <div class="legend-item">
        <div class="legend-color purple"></div>
        <span class="legend-text">关联人员数前五名并且非正常百分比小于50%</span>
      </div>
      <div class="legend-item">
        <div class="legend-color orange"></div>
        <span class="legend-text">关联人员数前五名</span>
      </div>
      <div class="legend-item">
        <div class="legend-color red"></div>
        <span class="legend-text">非正常百分比小于50%</span>
      </div>
      <div class="legend-item">
        <div class="legend-color blue"></div>
        <span class="legend-text">普通节点</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.graph-legend {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1000;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
  flex-shrink: 0;
}

.legend-color.purple {
  background-color: purple;
}

.legend-color.orange {
  background-color: #E6A23C;
}

.legend-color.red {
  background-color: red;
}

.legend-color.blue {
  background-color: #26b7ff;
}

.legend-text {
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}
</style>
