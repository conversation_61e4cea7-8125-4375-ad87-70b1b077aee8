<script setup>
import CustomDialog from "@/components/CustomDialog.vue";
import Graph from '@/components/graph.vue'
import { useTemplateRef, defineExpose, ref, computed, defineModel } from "vue";
import { formatNumberWithCommas, downloadBlob } from "@/utils/index.js";
import CustomButton from "@/components/CustomButton.vue";
import { downloadResult, getNodeRelation } from "@/api/index.js";
import { useRoute, useRouter } from "vue-router";
import { Search } from '@element-plus/icons-vue'

const route = useRoute();
const router = useRouter();

const dialogRef = useTemplateRef('dialog')
const graphRef = useTemplateRef('graph')
const showGraph = ref(true)
const orgGroupList = ref([])
const curIndex = ref(0)
const minConnections = defineModel('minConnections', {
  type: Number,
  default: 10
})

const emit = defineEmits(['nodeSelected', 'refresh'])

const curGroupList = computed(() => {
  return orgGroupList.value
    .map((item, idx) => ({ ...item, originIndex: idx }))
    .filter(item => item.nodes.some(node => node.id_num.includes(searchText.value)))
})

function generateTestData(n = 10, groupIndex = 0) {
  const nodes = []
  const links = []
  for (let i = 1; i <= n; i++) {
    nodes.push({
      name: `节点${i}`,
      id: `group${groupIndex}_node${i}`
    })
    if (i > 1) {
      // 连成一条链
      links.push({
        source: `group${groupIndex}_node${i - 1}`,
        target: `group${groupIndex}_node${i}`
      })
    }
  }
  return { nodes, links }
}

const testSeriesData = ref([
  /* {
    nodes: [
      {
        name: '节点1',
        id: 'node1'
      },
      {
        name: '节点2',
        id: 'node2'
      },
      {
        name: '节点3',
        id: 'node3'
      }
    ],
    links: [{ source: 'node1', target: 'node2' }, { source: 'node2', target: 'node3' }]
  } */
  /* generateTestData(10, 0),
  generateTestData(20, 1),
  generateTestData(30, 2),
  generateTestData(40, 3),
  generateTestData(50, 4),
  generateTestData(60, 5),
  generateTestData(70, 6) */
])
const groupList = ref([])
const searchText = ref('')


const handleDownload = async () => {
  graphRef.value.exportImg()
}
const handleDownloadExcel = async () => {
  const data = await downloadResult({ session_id: route.query.session_id });
  downloadBlob(data, 'xlsx', '研判结果');
}
const onResultClose = () => {
  router.push({ name: 'home', query: { notDelay: 1 } });
  setTimeout(() => {
    window.location.reload()
  })
};

const updateData = (data) => {
  orgGroupList.value = data
  graphRef.value.updateData([data[curIndex.value]])
}

const handleGroupClick = (index) => {
  graphRef.value.updateData([orgGroupList.value[index]])
  curIndex.value = index
}

const onNodeClick = (data) => {
  console.log(data)
  
  // 获取当前图谱数据
  const currentGraphData = orgGroupList.value[curIndex.value]
  
  // 获取当前节点ID
  const nodeId = data.id
  
  // 存储结果：相关节点和关系线
  const relatedNodes = []
  const relatedLinks = []
  
  // 存储节点ID，避免重复
  const nodeIdSet = new Set([nodeId])
  
  // 将中心节点添加到相关节点列表，并添加标识
  const centerNodeWithFlag = {
    ...data,
    isCenter: true  // 添加标识，表示这是中心节点
  }
  relatedNodes.push(centerNodeWithFlag)
  
  if (currentGraphData && currentGraphData.links && currentGraphData.nodes) {
    // 第一步：找出所有一级节点（与中心节点直接相连的节点）
    const firstLevelLinks = currentGraphData.links.filter(link => 
      link.source === nodeId || link.target === nodeId
    )
    
    // 添加一级关系线
    relatedLinks.push(...firstLevelLinks)
    
    // 获取一级节点ID和节点对象
    const firstLevelNodeIds = []
    firstLevelLinks.forEach(link => {
      const firstLevelNodeId = link.source === nodeId ? link.target : link.source
      if (!nodeIdSet.has(firstLevelNodeId)) {
        const firstLevelNode = currentGraphData.nodes.find(node => node.id === firstLevelNodeId)
        if (firstLevelNode) {
          // 添加一级节点标识
          relatedNodes.push({
            ...firstLevelNode,
            isFirstLevel: true  // 添加标识，表示这是一级节点
          })
          nodeIdSet.add(firstLevelNodeId)
          firstLevelNodeIds.push(firstLevelNodeId)
        }
      }
    })
    
    // 第二步：找出所有二级节点（与一级节点直接相连，但不与中心节点相连的节点）
    firstLevelNodeIds.forEach(firstLevelNodeId => {
      const secondLevelLinks = currentGraphData.links.filter(link => 
        (link.source === firstLevelNodeId || link.target === firstLevelNodeId) && 
        link.source !== nodeId && 
        link.target !== nodeId
      )
      
      // 添加二级关系线
      relatedLinks.push(...secondLevelLinks)
      
      // 获取二级节点ID和节点对象
      secondLevelLinks.forEach(link => {
        const secondLevelNodeId = link.source === firstLevelNodeId ? link.target : link.source
        if (!nodeIdSet.has(secondLevelNodeId)) {
          const secondLevelNode = currentGraphData.nodes.find(node => node.id === secondLevelNodeId)
          if (secondLevelNode) {
            // 添加二级节点标识
            relatedNodes.push({
              ...secondLevelNode,
              isSecondLevel: true  // 添加标识，表示这是二级节点
            })
            nodeIdSet.add(secondLevelNodeId)
          }
        }
      })
    })
  }
  
  console.log('相关节点(包括中心节点):', relatedNodes)
  console.log('相关关系线:', relatedLinks)
  
  // 创建节点关系图数据
  const nodeRelationData = {
    nodes: relatedNodes,
    links: relatedLinks
  }
  
  // 触发事件，将节点关系数据传递给父组件
  emit('nodeSelected', nodeRelationData)
}
/**
 * 
 * @param node 
 * session_id: str,  sessionid
    index: int = -1,  数据数组下标
    associated_id: str = "",  查询ID
    max_depth: int =5 查询层级
 */
const getNodeRelationData = async (node, depth = 2) => {
  const { code, data } = await getNodeRelation({ session_id: route.query.session_id, index: curIndex.value, associated_id: node.id, max_depth: depth });
  const nodeRelationData = {
    nodes: data.data.nodes,
    links: data.data.links
  }
  emit('nodeSelected', {
    nodeRelationData: nodeRelationData,
    node: node
  })
}
const onConnectionChange = (value) => {
  // searchText.value = ''
  // curIndex.value = 0
  emit('refresh', value)
}
const open = () => {
  dialogRef.value.open();
};
defineExpose({
  open,
  updateData,
  getNodeRelationData
});
</script>

<template>
  <custom-dialog :visible="false" ref="dialog" fullscreen show-close title="研判结果" @close="onResultClose">
    <div class="result-content flex-column">
      <div class="group-box">
        <el-input v-model="searchText" placeholder="搜索身份证" class="group-search-input"></el-input>
        <el-scrollbar class="group-list">
          <el-empty v-if="curGroupList.length === 0" description="暂无数据" :image-size="40" />
          <template v-else>
            <div class="group-item" v-for="(item, index) in curGroupList"
              :class="{ 'active': curIndex === item.originIndex }" :key="index"
              @click="handleGroupClick(item.originIndex)">
              关系{{ item.originIndex + 1 }}</div>
          </template>
        </el-scrollbar>
      </div>
      <div class="relation-count-select">
        <span>关系线数量</span>
        <el-select v-model="minConnections" placeholder="关系线数量" @change="onConnectionChange">
          <el-option v-for="item in 15" :key="item" :label="item" :value="item" />
        </el-select>
      </div>
      <div class="result-box">
        <graph v-if="showGraph" ref="graph" :series="testSeriesData" @node-click="getNodeRelationData" />
      </div>
      <div class="download-box">
        <CustomButton @click="handleDownloadExcel">下载数据</CustomButton>
        <CustomButton @click="handleDownload">下载图谱</CustomButton>
      </div>
    </div>
  </custom-dialog>
</template>

<style lang="scss" scoped>
.result-content {
  flex: 1;
  .group-box {
    position: absolute;
    top: 11vh;
    left: 20px;
    width: 200px;
    height: 450px;
    z-index: 999;
    .group-search-input {
      width: 100%;
      height: 32px;
      border-radius: 4px;
      
      margin-bottom: 10px;
      :deep(.el-input__wrapper) {
        background-color: rgba(255, 255, 255, 0.1);
        --el-input-border-color: #02AAE5;
        backdrop-filter: blur(10px);
        input {
          color: #fff;
        }
      }
    }

    .group-list {
      height: 100%;
      width: 100%;
      border: 1px solid #02AAE5;
      border-radius: 5px;
      background: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      box-sizing: border-box;
      .group-item {
        width: 100%;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        padding-left: 20px;
        color: #fff;
        box-sizing: border-box;

        &.active, &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }
  }

  .result-box {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 214px;

    .result-item {
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;

      .sum {
        display: flex;
        align-items: flex-end;
        line-height: 1;
        gap: 10px;
        padding-bottom: 23px;
        background: url("@/assets/images/dashed_line.png") no-repeat center bottom;
        margin-bottom: 20px;
        letter-spacing: 2px;

        h2 {
          font-size: 40px;
          line-height: 32px;
        }

        span {
          display: inline-block;
          font-size: 22px;
        }
      }

      .title {
        width: 160px;
        height: 114px;
        background: url("@/assets/images/result_item_bg.png") no-repeat center;
        font-size: 28px;
        line-height: 16px;
      }
    }
  }

  .download-box {
    position: absolute;
    right: 25px;
    bottom: 25px;
    display: flex;
    gap: 20px;

    :deep(.custom_btn) {
      width: 131px;
      height: 41.5px;
      font-size: 22px;
    }
  }
  .relation-count-select {
    width: 200px;
    position: absolute;
    left: 240px;
    top: 11vh;
    z-index: 999;
    display: flex;
    align-items: center;
    gap: 5px;
    > span {
      color: #fff;
      white-space: nowrap;
    }
    :deep(.el-select__placeholder) {
      color: #fff!important;
    }
    :deep(.el-select__wrapper) {
      background-color: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      --el-input-border-color: #02AAE5;
      box-shadow: none;
      border: 1px solid #02AAE5;
    }
  }
}
</style>